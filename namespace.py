import os
import logging
from pinecone import Pinecone
from dotenv import load_dotenv
from typing import List, Dict, Any

# Load environment variables
load_dotenv()

# Configuration
PINECONE_INDEX_NAME = "handbook-embedding"
PINECONE_API_KEY = os.getenv('PINECONE_API_KEY')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PineconeNamespaceExplorer:
    """Explore and analyze Pinecone index namespaces"""
    
    def __init__(self, api_key: str, index_name: str):
        """
        Initialize the Pinecone explorer
        
        Args:
            api_key: Pinecone API key
            index_name: Name of the Pinecone index
        """
        self.api_key = api_key
        self.index_name = index_name
        
        try:
            self.pc = Pinecone(api_key=self.api_key)
            self.index = self.pc.Index(self.index_name)
            logger.info(f"Connected to Pinecone index: {self.index_name}")
        except Exception as e:
            logger.error(f"Failed to connect to Pinecone: {e}")
            raise
    
    def list_all_namespaces(self) -> List[str]:
        """
        List all namespaces in the index
        
        Returns:
            List of namespace names
        """
        try:
            # Get index stats which includes namespace information
            stats = self.index.describe_index_stats()
            
            # Extract namespace names
            namespaces = list(stats.namespaces.keys()) if stats.namespaces else []
            
            logger.info(f"Found {len(namespaces)} namespaces in index '{self.index_name}'")
            return namespaces
            
        except Exception as e:
            logger.error(f"Error listing namespaces: {e}")
            return []
    
    def get_namespace_stats(self, namespace: str = None) -> Dict[str, Any]:
        """
        Get detailed statistics for a specific namespace or all namespaces
        
        Args:
            namespace: Specific namespace to analyze (None for all)
            
        Returns:
            Dictionary with namespace statistics
        """
        try:
            stats = self.index.describe_index_stats()
            
            if namespace:
                if namespace in stats.namespaces:
                    return {
                        "namespace": namespace,
                        "vector_count": stats.namespaces[namespace].vector_count,
                        "total_vector_count": stats.total_vector_count,
                        "dimension": stats.dimension,
                        "index_fullness": stats.index_fullness
                    }
                else:
                    logger.warning(f"Namespace '{namespace}' not found")
                    return {}
            else:
                # Return stats for all namespaces
                result = {
                    "total_vector_count": stats.total_vector_count,
                    "dimension": stats.dimension,
                    "index_fullness": stats.index_fullness,
                    "namespaces": {}
                }
                
                for ns_name, ns_stats in stats.namespaces.items():
                    result["namespaces"][ns_name] = {
                        "vector_count": ns_stats.vector_count
                    }
                
                return result
                
        except Exception as e:
            logger.error(f"Error getting namespace stats: {e}")
            return {}
    
    def sample_vectors_from_namespace(self, namespace: str, sample_size: int = 5) -> List[Dict]:
        """
        Get sample vectors from a namespace to understand the data structure
        
        Args:
            namespace: Namespace to sample from
            sample_size: Number of vectors to sample
            
        Returns:
            List of sample vector data
        """
        try:
            # Query with a dummy vector to get some results
            dummy_vector = [0.0] * 1408  # Assuming 1408 dimensions from your script
            
            results = self.index.query(
                vector=dummy_vector,
                top_k=sample_size,
                namespace=namespace,
                include_metadata=True
            )
            
            samples = []
            for match in results.matches:
                sample = {
                    "id": match.id,
                    "score": match.score,
                    "metadata": match.metadata
                }
                samples.append(sample)
            
            return samples
            
        except Exception as e:
            logger.error(f"Error sampling vectors from namespace '{namespace}': {e}")
            return []
    
    def print_namespace_summary(self):
        """Print a comprehensive summary of all namespaces"""
        print("\n" + "="*80)
        print(f"PINECONE INDEX ANALYSIS: {self.index_name}")
        print("="*80)
        
        # Get all namespaces
        namespaces = self.list_all_namespaces()
        
        if not namespaces:
            print("No namespaces found in the index.")
            return
        
        # Get overall stats
        overall_stats = self.get_namespace_stats()
        
        print(f"\nOVERALL INDEX STATISTICS:")
        print(f"  Total Vector Count: {overall_stats.get('total_vector_count', 'N/A'):,}")
        print(f"  Dimension: {overall_stats.get('dimension', 'N/A')}")
        print(f"  Index Fullness: {overall_stats.get('index_fullness', 'N/A'):.2%}")
        print(f"  Total Namespaces: {len(namespaces)}")
        
        print(f"\nNAMESPACE DETAILS:")
        print("-" * 80)
        
        for i, namespace in enumerate(namespaces, 1):
            ns_stats = self.get_namespace_stats(namespace)
            vector_count = ns_stats.get('vector_count', 0)
            
            print(f"\n{i}. Namespace: '{namespace}'")
            print(f"   Vector Count: {vector_count:,}")
            
            # Try to get sample metadata to understand the data structure
            samples = self.sample_vectors_from_namespace(namespace, 2)
            if samples:
                print(f"   Sample Vector IDs: {[s['id'] for s in samples[:2]]}")
                if samples[0].get('metadata'):
                    sample_metadata = samples[0]['metadata']
                    print(f"   Sample Metadata Keys: {list(sample_metadata.keys())}")
                    
                    # Show some key metadata fields if they exist
                    if 'handbook' in sample_metadata:
                        print(f"   Handbook: {sample_metadata['handbook']}")
                    if 'total_pages_processed' in sample_metadata:
                        print(f"   Total Pages: {sample_metadata['total_pages_processed']}")
                    if 'embedding_model' in sample_metadata:
                        print(f"   Model: {sample_metadata['embedding_model']}")
        
        print("\n" + "="*80)

def main():
    """Main function to explore Pinecone namespaces"""
    
    # Check if API key is available
    if not PINECONE_API_KEY:
        logger.error("PINECONE_API_KEY not found in environment variables")
        return
    
    try:
        # Initialize explorer
        explorer = PineconeNamespaceExplorer(PINECONE_API_KEY, PINECONE_INDEX_NAME)
        
        # Print comprehensive summary
        explorer.print_namespace_summary()
        
        # Also return the raw list for programmatic use
        namespaces = explorer.list_all_namespaces()
        
        print(f"\nRAW NAMESPACE LIST:")
        for namespace in namespaces:
            print(f"  - {namespace}")
        
    except Exception as e:
        logger.error(f"Error in main execution: {e}")

if __name__ == "__main__":
    main()